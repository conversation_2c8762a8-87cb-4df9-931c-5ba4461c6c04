from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select
from sqlalchemy.orm import Session
from sqlalchemy.orm.scoping import ScopedSession

from core.services.database.crud.base import CRUDBase
from core.services.database.schemas.conversation import ConversationTable
from core.services.database.schemas.conversation import (
    ConversationCreate,
    ConversationUpdate,
)


class ConversationCRUD(CRUDBase[ConversationTable, ConversationCreate, ConversationUpdate]):
    """对话记录CRUD操作实现"""

    async def get_by_user_id(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """根据用户ID获取对话列表（排除已删除的对话）"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.user_id == user_id)
            .where(ConversationTable.is_deleted.is_(False))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_agent_code(
        self, db: AsyncSession, *, agent_code: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """根据代理ID获取对话列表（排除已删除的对话）"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.agent_code == agent_code)
            .where(ConversationTable.is_deleted.is_(False))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def get_by_conversation_id(
        self,
        db: AsyncSession,
        *,
        _id: str,
        include_deleted: bool = False,
    ) -> List[ConversationTable]:
        """根据会话ID获取完整对话"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.id == _id)
            .order_by(ConversationTable.created_at)
        )
        if not include_deleted:
            query = query.where(ConversationTable.is_deleted.is_(False))
        result = await db.execute(query)
        return result.scalars().all()

    async def soft_delete(
        self, db: AsyncSession, *, _id: str
    ) -> Optional[ConversationTable]:
        """软删除对话记录"""
        obj = await self.get(db, _id)
        if obj:
            update_data = ConversationUpdate(is_deleted=True)
            return await self.update(db=db, db_obj=obj, obj_input=update_data)
        return None

    async def restore(
        self, db: AsyncSession, *, _id: str
    ) -> Optional[ConversationTable]:
        """恢复已删除的对话记录"""
        # 需要包含已删除的记录才能找到被软删除的对话
        obj = await self.get(db, _id, include_deleted=True)
        if obj:
            update_data = ConversationUpdate(is_deleted=False)
            return await self.update(db=db, db_obj=obj, obj_input=update_data)
        return None

    async def get_deleted_conversations(
        self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ConversationTable]:
        """获取已删除的对话列表（管理员功能）"""
        query = (
            select(ConversationTable)
            .where(ConversationTable.user_id == user_id)
            .where(ConversationTable.is_deleted.is_(True))
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(query)
        return result.scalars().all()

    async def search_conversations_by_message(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        keyword: str = "",
        agent_code: str = "",
        size: int = 10,
        offset: int = 0,
    ) -> List[Dict[str, Any]]:
        """
        根据关键字搜索会话和消息内容

        Args:
            db: 数据库会话
            user_id: 用户ID
            keyword: 搜索关键字
            agent_code: 代理代码（可选）
            size: 返回记录数
            offset: 偏移量

        Returns:
            包含会话信息和匹配消息内容的列表
        """
        # 替换通配符和特殊字符
        escaped_keyword = (
            keyword.replace("\\", "\\\\").replace("%", "\\%").replace("_", "\\_")
        )

        # 构建代理代码条件
        agent_code_condition = (
            "AND current_agent_code = :agent_code" if agent_code != "" else ""
        )

        # 构建搜索查询
        conversation_query = text(
            f"""
WITH FilteredConversation AS (
SELECT id, title, user_id, current_agent_code, updated_at FROM conversation as c
WHERE user_id = :user_id
AND is_deleted = false
{agent_code_condition}
AND
(
	EXISTS (
		SELECT content, created_at
		FROM message
		WHERE EXISTS (
				SELECT 1
				FROM jsonb_array_elements(content) AS item
				WHERE regexp_replace(item ->> 'data', '<message-embedded>.*?</message-embedded>', '', 'g') ILIKE :keyword
                AND item @> '{{"package_type": 0}}'
			)
			AND conversation_id = c.id
	)
	OR title ILIKE :keyword
)
ORDER BY updated_at DESC
LIMIT :size OFFSET :offset
)
SELECT fc.id, fc.title, fc.current_agent_code, fc.updated_at, content
FROM FilteredConversation AS fc
LEFT JOIN (
	SELECT DISTINCT ON (conversation_id) conversation_id, content
	FROM message
	WHERE EXISTS (
		SELECT 1
		FROM jsonb_array_elements(content) AS item
		WHERE regexp_replace(item ->> 'data', '<message-embedded>[\\s\\S]*</message-embedded>', '', 'g') ILIKE :keyword
        AND item @> '{{"package_type": 0}}'
	)
) as m ON m.conversation_id = fc.id
ORDER BY fc.updated_at DESC;
            """
        )

        # 执行查询
        result = await db.execute(
            conversation_query,
            {
                "user_id": user_id,
                "keyword": f"%{escaped_keyword}%",
                "size": size,
                "offset": offset,
                "agent_code": agent_code,
            },
        )

        return result.mappings().all()

    async def query_conversations_by_user_with_pagination(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        keyword: str = "",
        size: int = 10,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        根据用户ID查询会话列表，支持关键字搜索和分页

        Args:
            db: 数据库会话
            user_id: 用户ID
            keyword: 搜索关键字（可选）
            size: 每页记录数
            offset: 偏移量

        Returns:
            包含会话列表和总数的字典
        """
        # 替换通配符和特殊字符
        escaped_keyword = (
            keyword.replace("\\", "\\\\").replace("%", "\\%").replace("_", "\\_")
        )

        # 查询总数
        count_query = text(
            """
            SELECT COUNT(*) as count FROM conversation
            WHERE user_id = :user_id AND title LIKE :keyword AND is_deleted = false
        """
        )

        count_result = await db.execute(
            count_query, {"user_id": user_id, "keyword": f"%{escaped_keyword}%"}
        )
        total_row = count_result.mappings().one()
        total_count = total_row["count"] if total_row else 0

        # 查询会话列表
        conversation_query = text(
            """
            SELECT id, title, updated_at, current_agent_code
            FROM conversation
            WHERE user_id = :user_id AND title LIKE :keyword AND is_deleted = false
            ORDER BY updated_at DESC
            LIMIT :size OFFSET :offset
        """
        )

        conversations_result = await db.execute(
            conversation_query,
            {
                "user_id": user_id,
                "keyword": f"%{escaped_keyword}%",
                "size": size,
                "offset": offset,
            },
        )
        conversation_rows = conversations_result.mappings().all()

        return {"conversations": conversation_rows, "total_count": total_count}

    def create_sync(self, db: Session, obj_input: ConversationCreate) -> ConversationTable:
        """Synchronous version of create for thread usage"""
        db_obj = ConversationTable(**obj_input.model_dump())
        db.add(db_obj)
        db.flush()
        db.refresh(db_obj)
        return db_obj

conversation_curd = ConversationCRUD(ConversationTable)

